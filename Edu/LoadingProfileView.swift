import SwiftUI

struct LoadingProfileView: View {
    @State private var animationAmount: CGFloat = 1
    @State private var dotCount = 0
    @State private var progress: Double = 0.0
    @State private var currentStep = 0
    @State private var timer: Timer?
    @State private var progressTimer: Timer?
    
    let loadingSteps = [
        "Authenticating...",
        "Loading profile...",
        "Fetching children data...",
        "Almost ready..."
    ]
    
    var body: some View {
        ZStack {
            // Enhanced Background gradient
            DesignSystem.Colors.primaryGradient
                .ignoresSafeArea()

            VStack(spacing: DesignSystem.Spacing.xxl) {
                // Enhanced Animated Logo
                Image(systemName: "graduationcap.fill")
                    .font(.system(size: 84))
                    .foregroundColor(DesignSystem.Colors.textOnPrimary)
                    .scaleEffect(animationAmount)
                    .shadow(
                        color: DesignSystem.Shadow.medium.color,
                        radius: DesignSystem.Shadow.medium.radius,
                        x: DesignSystem.Shadow.medium.x,
                        y: DesignSystem.Shadow.medium.y
                    )
                    .onAppear {
                        withAnimation(.easeInOut(duration: 1.2).repeatForever(autoreverses: true)) {
                            animationAmount = 1.15
                        }
                    }

                VStack(spacing: DesignSystem.Spacing.lg) {
                    Text("Loading Profile")
                        .font(DesignSystem.Typography.title1)
                        .foregroundColor(DesignSystem.Colors.textOnPrimary)

                    // Enhanced current step text
                    Text(currentStep < loadingSteps.count ? loadingSteps[currentStep] : "Getting ready...")
                        .font(DesignSystem.Typography.bodyMedium)
                        .foregroundColor(DesignSystem.Colors.textOnPrimary.opacity(0.8))
                        .animation(.easeInOut(duration: 0.4), value: currentStep)
                    
                    // Enhanced Progress Bar
                    VStack(spacing: DesignSystem.Spacing.md) {
                        ZStack(alignment: .leading) {
                            // Background bar
                            RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.small)
                                .fill(DesignSystem.Colors.textOnPrimary.opacity(0.25))
                                .frame(width: 280, height: 10)

                            // Progress bar with gradient
                            RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.small)
                                .fill(
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            DesignSystem.Colors.textOnPrimary,
                                            DesignSystem.Colors.accent
                                        ]),
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    )
                                )
                                .frame(width: 280 * progress, height: 10)
                                .animation(.easeInOut(duration: 0.4), value: progress)
                        }
                        .shadow(
                            color: DesignSystem.Shadow.small.color,
                            radius: DesignSystem.Shadow.small.radius,
                            x: DesignSystem.Shadow.small.x,
                            y: DesignSystem.Shadow.small.y
                        )

                        // Enhanced progress percentage
                        Text("\(Int(progress * 100))%")
                            .font(DesignSystem.Typography.captionMedium)
                            .foregroundColor(DesignSystem.Colors.textOnPrimary.opacity(0.7))
                    }
                }

                // Enhanced loading indicator
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: DesignSystem.Colors.textOnPrimary))
                    .scaleEffect(1.6)
            }
        }
        .onAppear {
            // Reset progress when view appears
            progress = 0.0
            currentStep = 0
            
            // Start timers
            timer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: true) { _ in
                dotCount = (dotCount + 1) % 4
            }
            
            progressTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { _ in
                if progress < 1.0 {
                    progress += 0.033
                    
                    // Update current step based on progress
                    let newStep = min(Int(progress * Double(loadingSteps.count)), loadingSteps.count - 1)
                    if newStep != currentStep {
                        currentStep = newStep
                    }
                }
            }
        }
        .onDisappear {
            // Clean up timers
            timer?.invalidate()
            progressTimer?.invalidate()
        }
    }
}
