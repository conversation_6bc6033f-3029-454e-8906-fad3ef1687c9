import SwiftUI
import Foundation
import Security

// MARK: - Design System
struct DesignSystem {
    // MARK: - Colors
    struct Colors {
        // Primary Colors
        static let primary = Color(red: 0.2, green: 0.4, blue: 0.9)
        static let primaryDark = Color(red: 0.15, green: 0.3, blue: 0.7)
        static let secondary = Color(red: 0.5, green: 0.2, blue: 0.8)
        static let accent = Color(red: 0.0, green: 0.8, blue: 0.4)

        // Semantic Colors
        static let success = Color(red: 0.0, green: 0.7, blue: 0.3)
        static let warning = Color(red: 1.0, green: 0.6, blue: 0.0)
        static let error = Color(red: 0.9, green: 0.2, blue: 0.2)
        static let info = Color(red: 0.2, green: 0.6, blue: 0.9)

        // Neutral Colors
        static let background = Color(red: 0.98, green: 0.98, blue: 0.99)
        static let surface = Color.white
        static let surfaceSecondary = Color(red: 0.96, green: 0.97, blue: 0.98)
        static let border = Color(red: 0.9, green: 0.9, blue: 0.92)

        // Text Colors
        static let textPrimary = Color(red: 0.1, green: 0.1, blue: 0.1)
        static let textSecondary = Color(red: 0.4, green: 0.4, blue: 0.5)
        static let textTertiary = Color(red: 0.6, green: 0.6, blue: 0.65)
        static let textOnPrimary = Color.white

        // Gradient
        static let primaryGradient = LinearGradient(
            gradient: Gradient(colors: [primary.opacity(0.8), secondary.opacity(0.9)]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )

        static let cardGradient = LinearGradient(
            gradient: Gradient(colors: [surface, surfaceSecondary]),
            startPoint: .top,
            endPoint: .bottom
        )
    }

    // MARK: - Typography
    struct Typography {
        // Headings
        static let largeTitle = Font.system(size: 34, weight: .bold, design: .rounded)
        static let title1 = Font.system(size: 28, weight: .bold, design: .rounded)
        static let title2 = Font.system(size: 22, weight: .bold, design: .rounded)
        static let title3 = Font.system(size: 20, weight: .semibold, design: .rounded)

        // Body Text
        static let body = Font.system(size: 17, weight: .regular)
        static let bodyMedium = Font.system(size: 17, weight: .medium)
        static let bodySemibold = Font.system(size: 17, weight: .semibold)

        // Small Text
        static let caption = Font.system(size: 12, weight: .regular)
        static let captionMedium = Font.system(size: 12, weight: .medium)
        static let footnote = Font.system(size: 13, weight: .regular)
        static let footnoteMedium = Font.system(size: 13, weight: .medium)

        // Button Text
        static let buttonLarge = Font.system(size: 18, weight: .semibold)
        static let buttonMedium = Font.system(size: 16, weight: .semibold)
        static let buttonSmall = Font.system(size: 14, weight: .medium)
    }

    // MARK: - Spacing
    struct Spacing {
        static let xs: CGFloat = 4
        static let sm: CGFloat = 8
        static let md: CGFloat = 16
        static let lg: CGFloat = 24
        static let xl: CGFloat = 32
        static let xxl: CGFloat = 48
        static let xxxl: CGFloat = 64
    }

    // MARK: - Corner Radius
    struct CornerRadius {
        static let small: CGFloat = 8
        static let medium: CGFloat = 12
        static let large: CGFloat = 16
        static let extraLarge: CGFloat = 24
        static let round: CGFloat = 50
    }

    // MARK: - Shadows
    struct Shadow {
        static let small = (color: Color.black.opacity(0.1), radius: CGFloat(4), x: CGFloat(0), y: CGFloat(2))
        static let medium = (color: Color.black.opacity(0.15), radius: CGFloat(8), x: CGFloat(0), y: CGFloat(4))
        static let large = (color: Color.black.opacity(0.2), radius: CGFloat(16), x: CGFloat(0), y: CGFloat(8))
    }
}

// MARK: - Data Models
struct Child: Equatable {
    let id: String
    let name: String
    let photo: String?
    let school: String
}

struct SchoolResponse: Codable {
    let children: [String: ChildData]
    let schoolname: String
    
    struct ChildData: Codable {
        let name: String
        let photo: String?
        let school: String
    }
}

struct LoginResponse: Codable {
    let status: String
    let profile: String
}

// MARK: - Session Manager
class SessionManager: ObservableObject {
    static let shared = SessionManager()

    private let userDefaults = UserDefaults.standard
    private let keychainService = "com.fridatest.edu"

    // UserDefaults keys
    private let isRememberMeEnabledKey = "isRememberMeEnabled"
    private let usernameKey = "savedUsername"
    private let sessionCookieKey = "savedSessionCookie"

    // Keychain keys
    private let passwordKey = "savedPassword"

    private init() {}

    // MARK: - Save Session
    func saveSession(username: String, password: String, sessionCookie: String, rememberMe: Bool) {
        userDefaults.set(rememberMe, forKey: isRememberMeEnabledKey)

        if rememberMe {
            // Save username and session cookie in UserDefaults
            userDefaults.set(username, forKey: usernameKey)
            userDefaults.set(sessionCookie, forKey: sessionCookieKey)

            // Save password securely in Keychain
            savePasswordToKeychain(password: password)
        } else {
            // Clear saved data if remember me is disabled
            clearSavedSession()
        }
    }

    // MARK: - Load Session
    func loadSavedSession() -> (username: String, password: String, sessionCookie: String, rememberMe: Bool)? {
        let rememberMe = userDefaults.bool(forKey: isRememberMeEnabledKey)

        guard rememberMe else { return nil }

        guard let username = userDefaults.string(forKey: usernameKey),
              !username.isEmpty,
              let sessionCookie = userDefaults.string(forKey: sessionCookieKey),
              !sessionCookie.isEmpty,
              let password = loadPasswordFromKeychain(),
              !password.isEmpty else {
            // If any data is missing or empty, clear the session
            clearSavedSession()
            return nil
        }

        return (username: username, password: password, sessionCookie: sessionCookie, rememberMe: rememberMe)
    }

    // MARK: - Clear Session
    func clearSavedSession() {
        userDefaults.removeObject(forKey: isRememberMeEnabledKey)
        userDefaults.removeObject(forKey: usernameKey)
        userDefaults.removeObject(forKey: sessionCookieKey)
        deletePasswordFromKeychain()
    }

    // MARK: - Keychain Operations
    private func savePasswordToKeychain(password: String) {
        guard let passwordData = password.data(using: .utf8) else { return }

        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: keychainService,
            kSecAttrAccount as String: passwordKey,
            kSecValueData as String: passwordData
        ]

        // Delete existing item first
        SecItemDelete(query as CFDictionary)

        // Add new item
        let status = SecItemAdd(query as CFDictionary, nil)
        if status != errSecSuccess {
            print("Failed to save password to keychain: \(status)")
        }
    }

    private func loadPasswordFromKeychain() -> String? {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: keychainService,
            kSecAttrAccount as String: passwordKey,
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]

        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)

        guard status == errSecSuccess,
              let passwordData = result as? Data,
              let password = String(data: passwordData, encoding: .utf8) else {
            return nil
        }

        return password
    }

    private func deletePasswordFromKeychain() {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: keychainService,
            kSecAttrAccount as String: passwordKey
        ]

        SecItemDelete(query as CFDictionary)
    }
}

// MARK: - Pending Actions
enum PendingAction {
    case checkIn
    case checkOut
}

// MARK: - App State
class AppState: ObservableObject {
    @Published var isLoggedIn = false
    @Published var isLoadingProfile = false
    @Published var sessionCookie = ""
    @Published var username = ""
    @Published var pendingAction: PendingAction? = nil
    @Published var showingGeofencingSettings = false

    private let sessionManager = SessionManager.shared

    init() {
        loadSavedSession()
        setupNotificationObserver()
    }

    private func setupNotificationObserver() {
        NotificationCenter.default.addObserver(
            forName: .geofenceNotificationTapped,
            object: nil,
            queue: .main
        ) { notification in
            if let action = notification.object as? String {
                switch action {
                case "checkin":
                    self.pendingAction = .checkIn
                case "checkout":
                    self.pendingAction = .checkOut
                default:
                    break
                }

                // If not logged in, the pending action will be handled after login
                // If logged in, navigate to dashboard
                if self.isLoggedIn {
                    // The dashboard will handle the pending action
                }
            }
        }
    }

    // MARK: - Session Management
    func loadSavedSession() {
        if let savedSession = sessionManager.loadSavedSession() {
            // Auto-login with saved session - simplified approach
            self.username = savedSession.username
            self.sessionCookie = savedSession.sessionCookie
            self.isLoggedIn = true
        }
    }

    func saveSession(username: String, password: String, sessionCookie: String, rememberMe: Bool) {
        sessionManager.saveSession(username: username, password: password, sessionCookie: sessionCookie, rememberMe: rememberMe)
    }

    func clearSession() {
        sessionManager.clearSavedSession()
        self.isLoggedIn = false
        self.isLoadingProfile = false
        self.sessionCookie = ""
        self.username = ""
    }
}

// MARK: - Configuration
struct AppConfig {
    static let baseURL = "https://mobile.educabiz.com"
    static let defaultToken = "cUO7XmyD-03ygKyYyjep18%3AAPA91bEdsFtY6YPM1SQ4TI0UWtAeQojP96Gm2dafeCatGUEXw8VeIN2RK_ykUbMIb8pLi06v3AljFlmi4C_E0QuC51FavoXYkjJx8kD-wjiLRbCaywWfHzNaU"
}

// MARK: - Main Content View
struct ContentView: View {
    @EnvironmentObject var appState: AppState

    var body: some View {
        Group {
            if appState.isLoadingProfile {
                LoadingProfileView()
            } else if appState.isLoggedIn {
                DashboardView(appState: appState)
            } else {
                LoginView(appState: appState)
            }
        }
        .animation(.easeInOut(duration: 0.3), value: appState.isLoggedIn)
        .animation(.easeInOut(duration: 0.3), value: appState.isLoadingProfile)
        .sheet(isPresented: $appState.showingGeofencingSettings) {
            GeofencingSettingsView()
        }
    }
}

// MARK: - Preview
struct ContentView_Previews: PreviewProvider {
    static var previews: some View {
        ContentView()
            .environmentObject(AppState())
    }
}
