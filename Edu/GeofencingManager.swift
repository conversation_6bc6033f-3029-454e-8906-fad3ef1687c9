import Foundation
import CoreLocation
import UserNotifications

// MARK: - Geofencing Configuration
struct GeofencingConfig: Codable {
    let isEnabled: Bool
    let schoolAddress: String
    let latitude: Double
    let longitude: Double
    let radius: Double // in meters
    let schoolStartHour: Int // 24-hour format
    let schoolEndHour: Int // 24-hour format
    let schoolDays: [Int] // 1 = Sunday, 2 = Monday, etc.
    
    static let `default` = GeofencingConfig(
        isEnabled: false,
        schoolAddress: "",
        latitude: 0.0,
        longitude: 0.0,
        radius: 100.0,
        schoolStartHour: 7,
        schoolEndHour: 18,
        schoolDays: [2, 3, 4, 5, 6] // Monday to Friday
    )
}

// MARK: - Geofencing Manager
class GeofencingManager: NSObject, ObservableObject {
    static let shared = GeofencingManager()
    
    private let locationManager = CLLocationManager()
    private let userDefaults = UserDefaults.standard
    private let configKey = "geofencingConfig"
    private let lastCheckInNotificationKey = "lastCheckInNotification"
    private let lastCheckOutNotificationKey = "lastCheckOutNotification"
    
    @Published var config: GeofencingConfig
    @Published var locationPermissionStatus: CLAuthorizationStatus = .notDetermined
    @Published var isMonitoring: Bool = false
    
    // Callbacks for app integration
    var onGeofenceEntered: (() -> Void)?
    var onGeofenceExited: (() -> Void)?
    
    private override init() {
        // Load saved configuration
        if let data = userDefaults.data(forKey: configKey),
           let savedConfig = try? JSONDecoder().decode(GeofencingConfig.self, from: data) {
            self.config = savedConfig
        } else {
            self.config = GeofencingConfig.default
        }
        
        super.init()
        
        locationManager.delegate = self
        locationManager.desiredAccuracy = kCLLocationAccuracyHundredMeters // Lower power consumption
        locationPermissionStatus = locationManager.authorizationStatus
        
        // Start monitoring if enabled and permissions granted
        if config.isEnabled && locationPermissionStatus == .authorizedAlways {
            startMonitoring()
        }
    }
    
    // MARK: - Configuration Management
    func updateConfig(_ newConfig: GeofencingConfig) {
        let oldConfig = config
        config = newConfig
        saveConfig()
        
        // Restart monitoring if configuration changed
        if oldConfig.isEnabled != newConfig.isEnabled ||
           oldConfig.latitude != newConfig.latitude ||
           oldConfig.longitude != newConfig.longitude ||
           oldConfig.radius != newConfig.radius {
            
            if oldConfig.isEnabled {
                stopMonitoring()
            }
            
            if newConfig.isEnabled && locationPermissionStatus == .authorizedAlways {
                startMonitoring()
            }
        }
    }
    
    private func saveConfig() {
        if let data = try? JSONEncoder().encode(config) {
            userDefaults.set(data, forKey: configKey)
        }
    }
    
    // MARK: - Permission Management
    func requestLocationPermission() {
        switch locationPermissionStatus {
        case .notDetermined:
            locationManager.requestWhenInUseAuthorization()
        case .authorizedWhenInUse:
            locationManager.requestAlwaysAuthorization()
        case .denied, .restricted:
            // Show alert to go to settings
            break
        case .authorizedAlways:
            if config.isEnabled {
                startMonitoring()
            }
        @unknown default:
            break
        }
    }
    
    // MARK: - Geofence Monitoring
    private func startMonitoring() {
        guard config.isEnabled,
              locationPermissionStatus == .authorizedAlways,
              config.latitude != 0.0,
              config.longitude != 0.0 else {
            return
        }
        
        // Stop any existing monitoring
        stopMonitoring()
        
        // Create geofence region
        let center = CLLocationCoordinate2D(latitude: config.latitude, longitude: config.longitude)
        let region = CLCircularRegion(center: center, radius: config.radius, identifier: "school_geofence")
        region.notifyOnEntry = true
        region.notifyOnExit = true
        
        // Start monitoring
        locationManager.startMonitoring(for: region)
        isMonitoring = true
        
        print("Started geofence monitoring for school at \(config.schoolAddress)")
    }
    
    private func stopMonitoring() {
        locationManager.monitoredRegions.forEach { region in
            locationManager.stopMonitoring(for: region)
        }
        isMonitoring = false
        print("Stopped geofence monitoring")
    }
    
    // MARK: - School Hours Check
    private func isWithinSchoolHours() -> Bool {
        let now = Date()
        let calendar = Calendar.current
        let weekday = calendar.component(.weekday, from: now)
        let hour = calendar.component(.hour, from: now)
        
        // Check if it's a school day
        guard config.schoolDays.contains(weekday) else {
            return false
        }
        
        // Check if it's within school hours
        return hour >= config.schoolStartHour && hour <= config.schoolEndHour
    }
    
    // MARK: - Notification Management
    private func shouldSendCheckInNotification() -> Bool {
        guard isWithinSchoolHours() else { return false }
        
        let today = Calendar.current.startOfDay(for: Date())
        let lastNotification = userDefaults.object(forKey: lastCheckInNotificationKey) as? Date
        
        if let lastNotification = lastNotification {
            let lastNotificationDay = Calendar.current.startOfDay(for: lastNotification)
            return today > lastNotificationDay
        }
        
        return true
    }
    
    private func shouldSendCheckOutNotification() -> Bool {
        guard isWithinSchoolHours() else { return false }
        
        let today = Calendar.current.startOfDay(for: Date())
        let lastNotification = userDefaults.object(forKey: lastCheckOutNotificationKey) as? Date
        
        if let lastNotification = lastNotification {
            let lastNotificationDay = Calendar.current.startOfDay(for: lastNotification)
            return today > lastNotificationDay
        }
        
        return true
    }
    
    private func markCheckInNotificationSent() {
        userDefaults.set(Date(), forKey: lastCheckInNotificationKey)
    }
    
    private func markCheckOutNotificationSent() {
        userDefaults.set(Date(), forKey: lastCheckOutNotificationKey)
    }
    
    // MARK: - Notification Sending
    private func sendCheckInNotification() {
        guard shouldSendCheckInNotification() else { return }
        
        let content = UNMutableNotificationContent()
        content.title = "School Check-In"
        content.body = "You're near \(config.schoolAddress). Would you like to check in?"
        content.sound = .default
        content.userInfo = ["action": "checkin"]
        
        let request = UNNotificationRequest(identifier: "geofence_checkin", content: content, trigger: nil)
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("Error sending check-in notification: \(error)")
            } else {
                self.markCheckInNotificationSent()
                print("Check-in notification sent")
            }
        }
    }
    
    private func sendCheckOutNotification() {
        guard shouldSendCheckOutNotification() else { return }
        
        let content = UNMutableNotificationContent()
        content.title = "School Check-Out"
        content.body = "You're leaving \(config.schoolAddress). Would you like to check out?"
        content.sound = .default
        content.userInfo = ["action": "checkout"]
        
        let request = UNNotificationRequest(identifier: "geofence_checkout", content: content, trigger: nil)
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("Error sending check-out notification: \(error)")
            } else {
                self.markCheckOutNotificationSent()
                print("Check-out notification sent")
            }
        }
    }
}

// MARK: - CLLocationManagerDelegate
extension GeofencingManager: CLLocationManagerDelegate {
    func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {
        DispatchQueue.main.async {
            self.locationPermissionStatus = status
            
            switch status {
            case .authorizedAlways:
                if self.config.isEnabled {
                    self.startMonitoring()
                }
            case .denied, .restricted:
                self.stopMonitoring()
            default:
                break
            }
        }
    }
    
    func locationManager(_ manager: CLLocationManager, didEnterRegion region: CLRegion) {
        print("Entered geofence region: \(region.identifier)")
        
        // Add delay to avoid false triggers
        DispatchQueue.main.asyncAfter(deadline: .now() + 30) {
            if self.isWithinSchoolHours() {
                self.sendCheckInNotification()
                self.onGeofenceEntered?()
            }
        }
    }
    
    func locationManager(_ manager: CLLocationManager, didExitRegion region: CLRegion) {
        print("Exited geofence region: \(region.identifier)")
        
        // Add delay to avoid false triggers
        DispatchQueue.main.asyncAfter(deadline: .now() + 30) {
            if self.isWithinSchoolHours() {
                self.sendCheckOutNotification()
                self.onGeofenceExited?()
            }
        }
    }
    
    func locationManager(_ manager: CLLocationManager, monitoringDidFailFor region: CLRegion?, withError error: Error) {
        print("Geofence monitoring failed for region: \(region?.identifier ?? "unknown"), error: \(error)")
    }
}
