import SwiftUI

struct DashboardView: View {
    @ObservedObject var appState: AppState
    @StateObject private var apiService = APIService()
    @StateObject private var geofencingManager = GeofencingManager.shared
    @State private var selectedChild: Child?
    @State private var notes: String = ""
    @State private var statusMessage: String = ""
    @State private var responseJson: String = ""
    @State private var isLoading: Bool = false
    @State private var showAlert: Bool = false
    @State private var alertMessage: String = ""
    @State private var isSuccess: Bool = false
    @State private var showChildPicker = false
    @State private var pendingActionAfterChildSelection: PendingAction?
    @State private var showConfirmationModal = false
    @State private var confirmationChild: Child?
    @State private var confirmationAction: PendingAction?
    
    var body: some View {
        ZStack {
            backgroundView
            mainScrollView
        }
        .onAppear {
            setupInitialState()
        }
        .onChange(of: appState.pendingAction) { _ in
            handlePendingAction()
        }
        .onChange(of: showChildPicker) { isShowing in
            handleChildPickerChange(isShowing)
        }
        .onChange(of: selectedChild) { newChild in
            handleSelectedChildChange(newChild)
        }
        .onChange(of: apiService.children) { _ in
            handleChildrenChange()
        }
        .onTapGesture {
            hideKeyboard()
        }
        .alert("Error", isPresented: $showAlert) {
            Button("OK", role: .cancel) { }
        } message: {
            Text(alertMessage)
        }
        .sheet(isPresented: $showChildPicker) {
            ChildPickerView(
                children: apiService.children,
                selectedChild: $selectedChild,
                isPresented: $showChildPicker
            )
        }
        .sheet(isPresented: $showConfirmationModal) {
            ConfirmationModalView(
                child: confirmationChild,
                action: confirmationAction,
                onConfirm: {
                    executeConfirmedAction()
                    showConfirmationModal = false
                },
                onCancel: {
                    confirmationChild = nil
                    confirmationAction = nil
                    showConfirmationModal = false
                }
            )
        }
    }
    
    // MARK: - Background View
    private var backgroundView: some View {
        DesignSystem.Colors.primaryGradient
            .ignoresSafeArea()
    }
    
    // MARK: - Main Scroll View
    private var mainScrollView: some View {
        ScrollView {
            VStack(spacing: DesignSystem.Spacing.lg) {
                headerView
                mainContentCard
            }
        }
    }
    
    // MARK: - Header View
    private var headerView: some View {
        DashboardHeaderView(
            appState: appState,
            geofencingManager: geofencingManager,
            getCurrentDate: getCurrentDate
        )
    }
    
    // MARK: - Main Content Card
    private var mainContentCard: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            contentBasedOnState
        }
        .padding(.horizontal, DesignSystem.Spacing.lg)
        .padding(.vertical, DesignSystem.Spacing.lg)
        .background(cardBackground)
        .padding(.horizontal, DesignSystem.Spacing.lg)
    }
    
    // MARK: - Card Background
    private var cardBackground: some View {
        RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.extraLarge)
            .fill(.ultraThinMaterial)
            .shadow(
                color: DesignSystem.Shadow.large.color,
                radius: DesignSystem.Shadow.large.radius,
                x: DesignSystem.Shadow.large.x,
                y: DesignSystem.Shadow.large.y
            )
    }
    
    // MARK: - Content Based on State
    @ViewBuilder
    private var contentBasedOnState: some View {
        if apiService.isLoading {
            loadingView
        } else if !apiService.errorMessage.isEmpty {
            errorView
        } else if apiService.children.isEmpty {
            emptyChildrenView
        } else {
            childrenLoadedView
        }
    }
    
    // MARK: - Loading View
    private var loadingView: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: DesignSystem.Colors.primary))
                .scaleEffect(1.3)
            Text("Loading children...")
                .font(DesignSystem.Typography.bodyMedium)
                .foregroundColor(DesignSystem.Colors.textSecondary)
        }
        .frame(height: 140)
    }
    
    // MARK: - Error View
    private var errorView: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 44))
                .foregroundColor(DesignSystem.Colors.warning)

            Text("Error Loading Children")
                .font(DesignSystem.Typography.title3)
                .foregroundColor(DesignSystem.Colors.textPrimary)

            Text(apiService.errorMessage)
                .font(DesignSystem.Typography.body)
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, DesignSystem.Spacing.md)

            Button("Try Again") {
                apiService.fetchChildren(sessionCookie: appState.sessionCookie)
            }
            .buttonStyle(ProfessionalButtonStyle(color: DesignSystem.Colors.primary, style: .primary))
        }
        .padding(.vertical, DesignSystem.Spacing.lg)
    }
    
    // MARK: - Empty Children View
    private var emptyChildrenView: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            Image(systemName: "person.3.fill")
                .font(.system(size: 44))
                .foregroundColor(DesignSystem.Colors.textTertiary)

            Text("No Children Found")
                .font(DesignSystem.Typography.title3)
                .foregroundColor(DesignSystem.Colors.textPrimary)

            Button("Refresh") {
                apiService.fetchChildren(sessionCookie: appState.sessionCookie)
            }
            .buttonStyle(ProfessionalButtonStyle(color: DesignSystem.Colors.primary, style: .primary))
        }
        .padding(.vertical, DesignSystem.Spacing.lg)
    }
    
    // MARK: - Children Loaded View
    private var childrenLoadedView: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            childSelectionView
            notesInputView
            actionButtonsView
            statusMessageView

            // Developer Tools (<NAME_EMAIL>)
            if appState.username.lowercased() == "<EMAIL>" {
                developerToolsView
            }
        }
    }

    // MARK: - Developer Tools View
    private var developerToolsView: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            // Header
            HStack {
                Image(systemName: "hammer.fill")
                    .foregroundColor(DesignSystem.Colors.warning)
                Text("Developer Tools")
                    .font(DesignSystem.Typography.title3)
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                Spacer()
            }
            .padding(.bottom, DesignSystem.Spacing.sm)

            Divider()

            // Confirmation Modal Tests
            VStack(spacing: DesignSystem.Spacing.sm) {
                Text("Confirmation Modal Tests")
                    .font(DesignSystem.Typography.bodyMedium)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .frame(maxWidth: .infinity, alignment: .leading)

                HStack(spacing: DesignSystem.Spacing.sm) {
                    Button("Test Check-In Modal") {
                        testCheckInModal()
                    }
                    .buttonStyle(ProfessionalButtonStyle(color: DesignSystem.Colors.success, style: .secondary))

                    Button("Test Check-Out Modal") {
                        testCheckOutModal()
                    }
                    .buttonStyle(ProfessionalButtonStyle(color: DesignSystem.Colors.error, style: .secondary))
                }
            }

            Divider()

            // Push Notification Tests
            VStack(spacing: DesignSystem.Spacing.sm) {
                Text("Push Notification Tests")
                    .font(DesignSystem.Typography.bodyMedium)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .frame(maxWidth: .infinity, alignment: .leading)

                HStack(spacing: DesignSystem.Spacing.sm) {
                    Button("Send Check-In Push") {
                        sendTestCheckInNotification()
                    }
                    .buttonStyle(ProfessionalButtonStyle(color: DesignSystem.Colors.primary, style: .outline))

                    Button("Send Check-Out Push") {
                        sendTestCheckOutNotification()
                    }
                    .buttonStyle(ProfessionalButtonStyle(color: DesignSystem.Colors.primary, style: .outline))
                }
            }

            Divider()

            // Combined Tests
            VStack(spacing: DesignSystem.Spacing.sm) {
                Text("End-to-End Tests")
                    .font(DesignSystem.Typography.bodyMedium)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .frame(maxWidth: .infinity, alignment: .leading)

                Button("Full Flow: Push → Modal → Action") {
                    testFullNotificationFlow()
                }
                .buttonStyle(ProfessionalButtonStyle(color: DesignSystem.Colors.accent, style: .primary))
            }
        }
        .padding(DesignSystem.Spacing.md)
        .background(
            RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
                .fill(DesignSystem.Colors.warning.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
                        .stroke(DesignSystem.Colors.warning.opacity(0.2), lineWidth: 1)
                )
        )
    }

    // MARK: - Child Selection View
    private var childSelectionView: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Select Child")
                .font(.system(size: 18, weight: .semibold))
            
            Button(action: {
                showChildPicker = true
            }) {
                childSelectionButton
            }
        }
    }
    
    // MARK: - Child Selection Button
    private var childSelectionButton: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(selectedChild?.name ?? "Choose a child...")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(selectedChild == nil ? .gray : .black)
                
                if let child = selectedChild {
                    Text("ID: \(child.id)")
                        .font(.system(size: 12))
                        .foregroundColor(.gray)
                }
            }
            
            Spacer()
            
            Image(systemName: "chevron.down")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.gray)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.9))
        )
    }
    
    // MARK: - Notes Input View
    private var notesInputView: some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
            HStack {
                Image(systemName: "note.text")
                    .font(DesignSystem.Typography.footnote)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                Text("Notes (Optional)")
                    .font(DesignSystem.Typography.title3)
                    .foregroundColor(DesignSystem.Colors.textPrimary)
            }

            TextField("Enter notes or comments", text: $notes, axis: .vertical)
                .lineLimit(3...6)
                .padding(.horizontal, DesignSystem.Spacing.md)
                .padding(.vertical, DesignSystem.Spacing.md)
                .background(notesTextFieldBackground)
                .font(DesignSystem.Typography.body)
                .foregroundColor(DesignSystem.Colors.textPrimary)
        }
    }
    
    // MARK: - Notes Text Field Background
    private var notesTextFieldBackground: some View {
        RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
            .fill(DesignSystem.Colors.surface.opacity(0.95))
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
                    .stroke(DesignSystem.Colors.border.opacity(0.3), lineWidth: 1)
            )
            .shadow(
                color: DesignSystem.Shadow.small.color,
                radius: DesignSystem.Shadow.small.radius,
                x: DesignSystem.Shadow.small.x,
                y: DesignSystem.Shadow.small.y
            )
    }
    
    // MARK: - Action Buttons View
    private var actionButtonsView: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            checkInButton
            checkOutButton
        }
    }
    
    // MARK: - Check In Button
    private var checkInButton: some View {
        Button(action: {
            performCheckInOut(isCheckIn: true)
        }) {
            HStack(spacing: DesignSystem.Spacing.sm) {
                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.9)
                } else {
                    Image(systemName: "arrow.down.circle.fill")
                        .font(DesignSystem.Typography.buttonMedium)
                }

                Text("Check In")
                    .font(DesignSystem.Typography.buttonLarge)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 56)
            .background(checkInButtonBackground)
            .foregroundColor(DesignSystem.Colors.textOnPrimary)
            .scaleEffect(isLoading || selectedChild == nil ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.2), value: isLoading)
            .animation(.easeInOut(duration: 0.2), value: selectedChild == nil)
        }
        .disabled(isLoading || selectedChild == nil)
        .opacity((isLoading || selectedChild == nil) ? 0.7 : 1.0)
    }
    
    // MARK: - Check In Button Background
    private var checkInButtonBackground: some View {
        RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.large)
            .fill(DesignSystem.Colors.success)
            .shadow(
                color: DesignSystem.Shadow.medium.color,
                radius: DesignSystem.Shadow.medium.radius,
                x: DesignSystem.Shadow.medium.x,
                y: DesignSystem.Shadow.medium.y
            )
    }
    
    // MARK: - Check Out Button
    private var checkOutButton: some View {
        Button(action: {
            performCheckInOut(isCheckIn: false)
        }) {
            HStack(spacing: DesignSystem.Spacing.sm) {
                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.9)
                } else {
                    Image(systemName: "arrow.up.circle.fill")
                        .font(DesignSystem.Typography.buttonMedium)
                }

                Text("Check Out")
                    .font(DesignSystem.Typography.buttonLarge)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 56)
            .background(checkOutButtonBackground)
            .foregroundColor(DesignSystem.Colors.textOnPrimary)
            .scaleEffect(isLoading || selectedChild == nil ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.2), value: isLoading)
            .animation(.easeInOut(duration: 0.2), value: selectedChild == nil)
        }
        .disabled(isLoading || selectedChild == nil)
        .opacity((isLoading || selectedChild == nil) ? 0.7 : 1.0)
    }
    
    // MARK: - Check Out Button Background
    private var checkOutButtonBackground: some View {
        RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.large)
            .fill(DesignSystem.Colors.error)
            .shadow(
                color: DesignSystem.Shadow.medium.color,
                radius: DesignSystem.Shadow.medium.radius,
                x: DesignSystem.Shadow.medium.x,
                y: DesignSystem.Shadow.medium.y
            )
    }
    
    // MARK: - Status Message View
    @ViewBuilder
    private var statusMessageView: some View {
        if !statusMessage.isEmpty {
            VStack(spacing: 8) {
                HStack {
                    Image(systemName: isSuccess ? "checkmark.circle.fill" : "exclamationmark.triangle.fill")
                        .foregroundColor(isSuccess ? .green : .red)
                    
                    Text(statusMessage)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(isSuccess ? .green : .red)
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.white.opacity(0.9))
                )
            }
        }
    }
    
    // MARK: - Helper Methods
    private func setupInitialState() {
        if apiService.children.isEmpty {
            apiService.fetchChildren(sessionCookie: appState.sessionCookie)
        }
        handlePendingAction()
    }
    
    private func handleChildPickerChange(_ isShowing: Bool) {
        print("DEBUG: handleChildPickerChange - isShowing: \(isShowing)")
        if !isShowing && pendingActionAfterChildSelection != nil && selectedChild != nil {
            print("DEBUG: Child picker closed, executing pending action")
            executePendingAction()
        }
    }
    
    private func handleSelectedChildChange(_ newChild: Child?) {
        print("DEBUG: handleSelectedChildChange - newChild: \(newChild?.name ?? "nil")")
        if pendingActionAfterChildSelection != nil && newChild != nil && showChildPicker {
            print("DEBUG: Child selected while picker is showing, will execute when picker closes")
        }
    }
    
    private func handleChildrenChange() {
        if appState.pendingAction != nil {
            print("DEBUG: Children loaded, re-handling pending action")
            handlePendingAction()
        }
    }
    
    private func getCurrentDate() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "dd-MM-yyyy"
        return formatter.string(from: Date())
    }
    
    private func performCheckInOut(isCheckIn: Bool) {
        guard let child = selectedChild else {
            alertMessage = "Please select a child"
            showAlert = true
            return
        }
        
        let action = isCheckIn ? "Check In" : "Check Out"
        
        isLoading = true
        statusMessage = "\(action) in progress for \(child.name)..."
        responseJson = ""
        isSuccess = false
        
        apiService.performCheckInOut(sessionCookie: appState.sessionCookie, childId: child.id, notes: notes, isCheckIn: isCheckIn) { success, message, jsonResponse in
            isLoading = false
            responseJson = jsonResponse
            
            if success {
                statusMessage = "\(action) successful for \(child.name)!"
                isSuccess = true
                notes = ""
            } else {
                statusMessage = "\(action) failed for \(child.name): \(message)"
                isSuccess = false
            }
        }
    }
    
    private func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
    
    private func handlePendingAction() {
        guard let pendingAction = appState.pendingAction else {
            print("DEBUG: No pending action found")
            return
        }

        print("DEBUG: Handling pending action: \(pendingAction)")
        print("DEBUG: Children count: \(apiService.children.count)")

        if apiService.children.isEmpty {
            print("DEBUG: No children loaded yet, waiting...")
            return
        }

        appState.pendingAction = nil

        if apiService.children.count == 1 {
            selectedChild = apiService.children.first
            print("DEBUG: Single child found, showing confirmation for: \(apiService.children.first?.name ?? "Unknown")")
            showConfirmationForAction(child: apiService.children.first!, action: pendingAction)
        } else if apiService.children.count > 1 {
            print("DEBUG: Multiple children found, showing picker")
            pendingActionAfterChildSelection = pendingAction
            showChildPicker = true
        }
    }

    private func executePendingAction() {
        guard let pendingAction = pendingActionAfterChildSelection,
              let child = selectedChild else { 
            print("DEBUG: executePendingAction failed")
            return 
        }

        print("DEBUG: executePendingAction - about to show confirmation for \(child.name), action: \(pendingAction)")
        pendingActionAfterChildSelection = nil
        showConfirmationForAction(child: child, action: pendingAction)
    }

    private func showConfirmationForAction(child: Child, action: PendingAction) {
        print("DEBUG: Setting up confirmation modal for child: \(child.name), action: \(action)")        
        confirmationChild = child
        confirmationAction = action
        showConfirmationModal = true
        print("DEBUG: showConfirmationModal set to: \(showConfirmationModal)")
    }

    private func executeConfirmedAction() {
        guard let action = confirmationAction else { return }

        switch action {
        case .checkIn:
            performCheckInOut(isCheckIn: true)
        case .checkOut:
            performCheckInOut(isCheckIn: false)
        }

        confirmationChild = nil
        confirmationAction = nil
    }

    // MARK: - Developer Tool Functions
    private func testCheckInModal() {
        guard let firstChild = apiService.children.first else { return }
        showConfirmationForAction(child: firstChild, action: .checkIn)
    }

    private func testCheckOutModal() {
        guard let firstChild = apiService.children.first else { return }
        showConfirmationForAction(child: firstChild, action: .checkOut)
    }

    private func sendTestCheckInNotification() {
        let content = UNMutableNotificationContent()
        content.title = "🧪 DEV: School Check-In"
        content.body = "Developer test: You're near school. Would you like to check in?"
        content.sound = .default
        content.userInfo = ["action": "checkin"]

        let request = UNNotificationRequest(identifier: "dev_test_checkin_\(Date().timeIntervalSince1970)", content: content, trigger: nil)

        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("DEV: Error sending test check-in notification: \(error)")
            } else {
                print("DEV: Test check-in notification sent successfully")
            }
        }
    }

    private func sendTestCheckOutNotification() {
        let content = UNMutableNotificationContent()
        content.title = "🧪 DEV: School Check-Out"
        content.body = "Developer test: You're leaving school. Would you like to check out?"
        content.sound = .default
        content.userInfo = ["action": "checkout"]

        let request = UNNotificationRequest(identifier: "dev_test_checkout_\(Date().timeIntervalSince1970)", content: content, trigger: nil)

        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("DEV: Error sending test check-out notification: \(error)")
            } else {
                print("DEV: Test check-out notification sent successfully")
            }
        }
    }

    private func testFullNotificationFlow() {
        // Send a notification that will trigger the full flow
        let content = UNMutableNotificationContent()
        content.title = "🧪 DEV: Full Flow Test"
        content.body = "Testing complete notification → modal → action flow"
        content.sound = .default
        content.userInfo = ["action": "checkin"]

        let request = UNNotificationRequest(identifier: "dev_full_flow_\(Date().timeIntervalSince1970)", content: content, trigger: nil)

        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("DEV: Error sending full flow test notification: \(error)")
            } else {
                print("DEV: Full flow test notification sent - tap it to test the complete flow!")
            }
        }
    }
}

// MARK: - Professional Button Style
struct ProfessionalButtonStyle: ButtonStyle {
    let color: Color
    let style: ButtonStyleType

    enum ButtonStyleType {
        case primary, secondary, outline
    }

    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .padding(.horizontal, DesignSystem.Spacing.lg)
            .padding(.vertical, DesignSystem.Spacing.md)
            .background(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
                    .fill(backgroundForStyle())
                    .overlay(
                        RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
                            .stroke(borderColorForStyle(), lineWidth: style == .outline ? 2 : 0)
                    )
                    .shadow(
                        color: DesignSystem.Shadow.small.color,
                        radius: DesignSystem.Shadow.small.radius,
                        x: DesignSystem.Shadow.small.x,
                        y: DesignSystem.Shadow.small.y
                    )
            )
            .foregroundColor(textColorForStyle())
            .font(DesignSystem.Typography.buttonMedium)
            .scaleEffect(configuration.isPressed ? 0.96 : 1.0)
            .animation(.easeInOut(duration: 0.15), value: configuration.isPressed)
    }

    private func backgroundForStyle() -> Color {
        switch style {
        case .primary:
            return color
        case .secondary:
            return color.opacity(0.1)
        case .outline:
            return Color.clear
        }
    }

    private func textColorForStyle() -> Color {
        switch style {
        case .primary:
            return .white
        case .secondary, .outline:
            return color
        }
    }

    private func borderColorForStyle() -> Color {
        return color
    }
}

// MARK: - Confirmation Modal View
struct ConfirmationModalView: View {
    let child: Child?
    let action: PendingAction?
    let onConfirm: () -> Void
    let onCancel: () -> Void

    var body: some View {
        VStack(spacing: 0) {
            // Top drag indicator area
            VStack(spacing: 16) {
                // Drag indicator
                RoundedRectangle(cornerRadius: 2.5)
                    .fill(DesignSystem.Colors.textTertiary.opacity(0.3))
                    .frame(width: 36, height: 5)
                    .padding(.top, 8)
                
                // Icon and title section
                VStack(spacing: 12) {
                    // Large action icon with background
                    ZStack {
                        Circle()
                            .fill(actionColor.opacity(0.1))
                            .frame(width: 80, height: 80)
                        
                        Image(systemName: actionIcon)
                            .font(.system(size: 32, weight: .medium))
                            .foregroundColor(actionColor)
                    }
                    
                    VStack(spacing: 4) {
                        Text("Confirm Action")
                            .font(DesignSystem.Typography.title2)
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                        
                        Text("Please confirm this check-\(actionText.lowercased())")
                            .font(DesignSystem.Typography.caption)
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    }
                }
            }
            .padding(.horizontal, 24)
            
            // Child information card
            VStack(spacing: 16) {
                if let child = child {
                    VStack(spacing: 12) {
                        HStack {
                            Image(systemName: "person.crop.circle.fill")
                                .font(.title2)
                                .foregroundColor(DesignSystem.Colors.primary)
                            
                            VStack(alignment: .leading, spacing: 2) {
                                Text("Student")
                                    .font(DesignSystem.Typography.caption)
                                    .foregroundColor(DesignSystem.Colors.textSecondary)
                                
                                Text(child.name)
                                    .font(DesignSystem.Typography.body)
                                    .foregroundColor(DesignSystem.Colors.textPrimary)
                            }
                            
                            Spacer()
                        }
                        .padding(.horizontal, 24)
                        
                        HStack {
                            Image(systemName: "clock.fill")
                                .font(.title2)
                                .foregroundColor(DesignSystem.Colors.info)
                            
                            VStack(alignment: .leading, spacing: 2) {
                                Text("Time")
                                    .font(DesignSystem.Typography.caption)
                                    .foregroundColor(DesignSystem.Colors.textSecondary)
                                
                                Text(getCurrentTime())
                                    .font(DesignSystem.Typography.body)
                                    .foregroundColor(DesignSystem.Colors.textPrimary)
                            }
                            
                            Spacer()
                        }
                        .padding(.horizontal, 24)
                    }
                }
            }
            
            // Action buttons
            VStack(spacing: 12) {
                Divider()
                    .padding(.horizontal, 24)
                
                VStack(spacing: 12) {
                    Button(action: onConfirm) {
                        HStack(spacing: 8) {
                            Image(systemName: actionIcon)
                                .font(.system(size: 16, weight: .semibold))
                            
                            Text("\(actionText) \(child?.name.components(separatedBy: " ").first ?? "Student")")
                                .font(DesignSystem.Typography.buttonLarge)
                                .fontWeight(.semibold)
                        }
                        .frame(maxWidth: .infinity)
                        .frame(height: 52)
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(actionColor)
                        )
                        .foregroundColor(.white)
                    }
                    
                    Button(action: onCancel) {
                        Text("Cancel")
                            .font(DesignSystem.Typography.buttonMedium)
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .frame(maxWidth: .infinity)
                            .frame(height: 44)
                    }
                }
                .padding(.horizontal, 24)
                .padding(.bottom, 8)
            }
        }
        .background(
            RoundedRectangle(cornerRadius: 24)
                .fill(DesignSystem.Colors.surface)
                .shadow(
                    color: DesignSystem.Shadow.medium.color.opacity(0.3),
                    radius: 20,
                    x: 0,
                    y: -5
                )
        )
        .presentationDetents([.height(420)])
        .presentationDragIndicator(.hidden)
        .presentationBackground(.clear)
    }
    
    // MARK: - Computed Properties
    private var actionText: String {
        action == .checkIn ? "Check In" : "Check Out"
    }
    
    private var actionIcon: String {
        action == .checkIn ? "arrow.down.circle.fill" : "arrow.up.circle.fill"
    }
    
    private var actionColor: Color {
        action == .checkIn ? DesignSystem.Colors.success : DesignSystem.Colors.error
    }
    
    private func getCurrentTime() -> String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        formatter.dateStyle = .none
        return formatter.string(from: Date())
    }
}
