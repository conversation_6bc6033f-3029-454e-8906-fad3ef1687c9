import SwiftUI

struct LoginView: View {
    @ObservedObject var appState: AppState
    @StateObject private var apiService = APIService()

    @State private var username = ""
    @State private var password = ""
    @State private var token = AppConfig.defaultToken
    @State private var isLoading = false
    @State private var showAlert = false
    @State private var alertMessage = ""
    @State private var rememberMe = true

    private let sessionManager = SessionManager.shared
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Enhanced Background gradient
                DesignSystem.Colors.primaryGradient
                    .ignoresSafeArea()

                VStack(spacing: 0) {
                    Spacer()

                    // Enhanced Login Card
                    VStack(spacing: DesignSystem.Spacing.xl) {
                        // Logo/Title Section with improved styling
                        VStack(spacing: DesignSystem.Spacing.lg) {
                            // Logo with subtle animation
                            Image(systemName: "graduationcap.fill")
                                .font(.system(size: 64))
                                .foregroundColor(DesignSystem.Colors.textOnPrimary)
                                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)

                            VStack(spacing: DesignSystem.Spacing.sm) {
                                Text("School Check-In")
                                    .font(DesignSystem.Typography.largeTitle)
                                    .foregroundColor(DesignSystem.Colors.textOnPrimary)
                                    .multilineTextAlignment(.center)

                                Text("Access your school portal securely")
                                    .font(DesignSystem.Typography.bodyMedium)
                                    .foregroundColor(DesignSystem.Colors.textOnPrimary.opacity(0.8))
                                    .multilineTextAlignment(.center)
                            }
                        }
                        .padding(.top, DesignSystem.Spacing.xl)
                        
                        // Enhanced Form Section
                        VStack(spacing: DesignSystem.Spacing.lg) {
                            // Username Field with improved styling
                            VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
                                HStack {
                                    Image(systemName: "envelope")
                                        .font(DesignSystem.Typography.footnote)
                                        .foregroundColor(DesignSystem.Colors.textOnPrimary.opacity(0.8))
                                    Text("Email Address")
                                        .font(DesignSystem.Typography.footnoteMedium)
                                        .foregroundColor(DesignSystem.Colors.textOnPrimary.opacity(0.9))
                                }

                                TextField("Enter your email", text: $username)
                                    .textFieldStyle(ProfessionalTextFieldStyle())
                                    .keyboardType(.emailAddress)
                                    .autocapitalization(.none)
                                    .disabled(isLoading)
                            }

                            // Password Field with improved styling
                            VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
                                HStack {
                                    Image(systemName: "lock")
                                        .font(DesignSystem.Typography.footnote)
                                        .foregroundColor(DesignSystem.Colors.textOnPrimary.opacity(0.8))
                                    Text("Password")
                                        .font(DesignSystem.Typography.footnoteMedium)
                                        .foregroundColor(DesignSystem.Colors.textOnPrimary.opacity(0.9))
                                }

                                SecureField("Enter your password", text: $password)
                                    .textFieldStyle(ProfessionalTextFieldStyle())
                                    .disabled(isLoading)
                            }

                            // Enhanced Remember Me Toggle
                            HStack {
                                Toggle("Remember me for next time", isOn: $rememberMe)
                                    .font(DesignSystem.Typography.footnote)
                                    .foregroundColor(DesignSystem.Colors.textOnPrimary.opacity(0.9))
                                    .disabled(isLoading)
                                    .toggleStyle(ProfessionalToggleStyle())

                                Spacer()
                            }
                            
                            // Enhanced Login Button
                            Button(action: performLogin) {
                                HStack(spacing: DesignSystem.Spacing.sm) {
                                    if isLoading {
                                        ProgressView()
                                            .progressViewStyle(CircularProgressViewStyle(tint: DesignSystem.Colors.primary))
                                            .scaleEffect(0.9)
                                    } else {
                                        Image(systemName: "arrow.right.circle.fill")
                                            .font(DesignSystem.Typography.buttonMedium)
                                    }

                                    Text(isLoading ? "Signing In..." : "Sign In")
                                        .font(DesignSystem.Typography.buttonLarge)
                                }
                                .frame(maxWidth: .infinity)
                                .frame(height: 56)
                                .background(
                                    RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.large)
                                        .fill(DesignSystem.Colors.surface)
                                        .shadow(
                                            color: DesignSystem.Shadow.medium.color,
                                            radius: DesignSystem.Shadow.medium.radius,
                                            x: DesignSystem.Shadow.medium.x,
                                            y: DesignSystem.Shadow.medium.y
                                        )
                                )
                                .foregroundColor(DesignSystem.Colors.primary)
                                .scaleEffect(isLoading || username.isEmpty || password.isEmpty ? 0.98 : 1.0)
                                .animation(.easeInOut(duration: 0.2), value: isLoading)
                                .animation(.easeInOut(duration: 0.2), value: username.isEmpty)
                                .animation(.easeInOut(duration: 0.2), value: password.isEmpty)
                            }
                            .disabled(isLoading || username.isEmpty || password.isEmpty)
                            .opacity((isLoading || username.isEmpty || password.isEmpty) ? 0.7 : 1.0)
                        }
                        .padding(.horizontal, DesignSystem.Spacing.xl)

                        Spacer()
                    }
                    .frame(maxWidth: 420)
                    .background(
                        RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.extraLarge)
                            .fill(.ultraThinMaterial)
                            .shadow(
                                color: DesignSystem.Shadow.large.color,
                                radius: DesignSystem.Shadow.large.radius,
                                x: DesignSystem.Shadow.large.x,
                                y: DesignSystem.Shadow.large.y
                            )
                    )
                    .padding(.horizontal, DesignSystem.Spacing.lg)
                    
                    Spacer()
                }
            }
        }
        .onTapGesture {
            hideKeyboard()
        }
        .alert("Login Error", isPresented: $showAlert) {
            Button("OK", role: .cancel) { }
        } message: {
            Text(alertMessage)
        }
        .onAppear {
            loadSavedCredentials()
        }
    }
    
    private func performLogin() {
        // Validate inputs
        guard !username.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            alertMessage = "Please enter your email"
            showAlert = true
            return
        }
        
        guard isValidEmail(username) else {
            alertMessage = "Please enter a valid email address"
            showAlert = true
            return
        }
        
        guard !password.isEmpty else {
            alertMessage = "Please enter your password"
            showAlert = true
            return
        }
        
        isLoading = true
        
        apiService.login(username: username, password: password, token: token) { success, sessionCookie, response in
            // No need to dispatch to main thread here since APIService already does it
            self.isLoading = false
            
            if success {
                // Parse login response
                if let data = response.data(using: .utf8) {
                    do {
                        let loginResponse = try JSONDecoder().decode(LoginResponse.self, from: data)
                        if loginResponse.status == "ok" {
                            // Store credentials
                            self.appState.sessionCookie = sessionCookie
                            self.appState.username = self.username

                            // Save session if remember me is enabled
                            self.appState.saveSession(
                                username: self.username,
                                password: self.password,
                                sessionCookie: sessionCookie,
                                rememberMe: self.rememberMe
                            )

                            // Immediately show loading screen
                            self.appState.isLoadingProfile = true

                            // Hold loading screen for 3 seconds for style, then show dashboard
                            DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                                self.appState.isLoggedIn = true
                                self.appState.isLoadingProfile = false

                                // If there's a pending action from notification, it will be handled in DashboardView
                            }
                        } else {
                            self.alertMessage = "Login failed: Invalid credentials"
                            self.showAlert = true
                        }
                    } catch {
                        self.alertMessage = "Login failed: Could not parse response"
                        self.showAlert = true
                    }
                } else {
                    self.alertMessage = "Login failed: Invalid response"
                    self.showAlert = true
                }
            } else {
                self.alertMessage = "Login failed: \(response)"
                self.showAlert = true
            }
        }
    }
    
    private func isValidEmail(_ email: String) -> Bool {
        let emailRegEx = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPred = NSPredicate(format:"SELF MATCHES %@", emailRegEx)
        return emailPred.evaluate(with: email)
    }

    private func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }

    private func loadSavedCredentials() {
        if let savedSession = sessionManager.loadSavedSession() {
            username = savedSession.username
            password = savedSession.password
            rememberMe = savedSession.rememberMe
        }
    }
}

// MARK: - Professional Text Field Style
struct ProfessionalTextFieldStyle: TextFieldStyle {
    func _body(configuration: TextField<Self._Label>) -> some View {
        configuration
            .padding(.horizontal, DesignSystem.Spacing.md)
            .padding(.vertical, DesignSystem.Spacing.md)
            .background(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
                    .fill(DesignSystem.Colors.surface.opacity(0.95))
                    .overlay(
                        RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
                            .stroke(DesignSystem.Colors.border.opacity(0.3), lineWidth: 1)
                    )
                    .shadow(
                        color: DesignSystem.Shadow.small.color,
                        radius: DesignSystem.Shadow.small.radius,
                        x: DesignSystem.Shadow.small.x,
                        y: DesignSystem.Shadow.small.y
                    )
            )
            .font(DesignSystem.Typography.body)
            .foregroundColor(DesignSystem.Colors.textPrimary)
    }
}

// MARK: - Professional Toggle Style
struct ProfessionalToggleStyle: ToggleStyle {
    func makeBody(configuration: Configuration) -> some View {
        HStack(spacing: DesignSystem.Spacing.sm) {
            RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.large)
                .fill(configuration.isOn ? DesignSystem.Colors.accent : DesignSystem.Colors.surface.opacity(0.3))
                .frame(width: 44, height: 24)
                .overlay(
                    Circle()
                        .fill(DesignSystem.Colors.surface)
                        .frame(width: 20, height: 20)
                        .offset(x: configuration.isOn ? 10 : -10)
                        .animation(.easeInOut(duration: 0.2), value: configuration.isOn)
                )
                .onTapGesture {
                    configuration.isOn.toggle()
                }

            configuration.label
        }
    }
}

