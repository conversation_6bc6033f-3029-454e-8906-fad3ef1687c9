import SwiftUI

struct DashboardHeaderView: View {
    @ObservedObject var appState: AppState
    @ObservedObject var geofencingManager: GeofencingManager
    var getCurrentDate: () -> String

    var body: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            HStack {
                VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                    Text("Welcome back!")
                        .font(DesignSystem.Typography.bodyMedium)
                        .foregroundColor(DesignSystem.Colors.textOnPrimary.opacity(0.8))

                    Text("School Check-In")
                        .font(DesignSystem.Typography.title1)
                        .foregroundColor(DesignSystem.Colors.textOnPrimary)
                }

                Spacer()

                HStack(spacing: DesignSystem.Spacing.sm) {
                    Button(action: {
                        appState.showingGeofencingSettings = true
                    }) {
                        Image(systemName: "location.circle")
                            .font(DesignSystem.Typography.buttonMedium)
                    }
                    .foregroundColor(DesignSystem.Colors.textOnPrimary.opacity(0.9))
                    .padding(DesignSystem.Spacing.sm)
                    .background(
                        RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.large)
                            .fill(.ultraThinMaterial)
                            .shadow(
                                color: DesignSystem.Shadow.small.color,
                                radius: DesignSystem.Shadow.small.radius,
                                x: DesignSystem.Shadow.small.x,
                                y: DesignSystem.Shadow.small.y
                            )
                    )

                    Button("Logout") {
                        appState.clearSession()
                    }
                    .font(DesignSystem.Typography.buttonSmall)
                    .foregroundColor(DesignSystem.Colors.textOnPrimary.opacity(0.9))
                    .padding(.horizontal, DesignSystem.Spacing.md)
                    .padding(.vertical, DesignSystem.Spacing.sm)
                    .background(
                        RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.large)
                            .fill(.ultraThinMaterial)
                            .shadow(
                                color: DesignSystem.Shadow.small.color,
                                radius: DesignSystem.Shadow.small.radius,
                                x: DesignSystem.Shadow.small.x,
                                y: DesignSystem.Shadow.small.y
                            )
                    )
                }
            }

            HStack {
                Image(systemName: "calendar")
                    .font(DesignSystem.Typography.footnote)
                    .foregroundColor(DesignSystem.Colors.textOnPrimary.opacity(0.7))

                Text("Today: \(getCurrentDate())")
                    .font(DesignSystem.Typography.bodyMedium)
                    .foregroundColor(DesignSystem.Colors.textOnPrimary.opacity(0.8))

                Spacer()

                if geofencingManager.config.isEnabled && geofencingManager.isMonitoring {
                    HStack(spacing: DesignSystem.Spacing.xs) {
                        Circle()
                            .fill(DesignSystem.Colors.accent)
                            .frame(width: 8, height: 8)
                        Text("Geofencing Active")
                            .font(DesignSystem.Typography.caption)
                            .foregroundColor(DesignSystem.Colors.textOnPrimary.opacity(0.7))
                    }
                }
            }
        }
        .padding(.horizontal, DesignSystem.Spacing.lg)
        .padding(.top, DesignSystem.Spacing.sm)
    }
}
