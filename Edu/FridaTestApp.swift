import SwiftUI
import UserNotifications

@main
struct Edu: App {
    @StateObject private var appState = AppState()

    init() {
        // Request notification permissions
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .sound, .badge]) { granted, error in
            if granted {
                print("Notification permission granted")
            } else if let error = error {
                print("Notification permission error: \(error)")
            }
        }

        // Set notification delegate
        UNUserNotificationCenter.current().delegate = NotificationDelegate.shared

        // Initialize geofencing manager
        _ = GeofencingManager.shared
    }

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(appState)
                .onOpenURL { url in
                    handleDeepLink(url)
                }
        }
    }

    private func handleDeepLink(_ url: URL) {
        // Handle deep links from notifications
        if url.scheme == "eduapp" {
            switch url.host {
            case "checkin":
                appState.pendingAction = .checkIn
            case "checkout":
                appState.pendingAction = .checkOut
            default:
                break
            }
        }
    }
}

// MARK: - Notification Delegate
class NotificationDelegate: NSObject, UNUserNotificationCenterDelegate {
    static let shared = NotificationDelegate()

    // Handle notification when app is in foreground
    func userNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification, withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
        completionHandler([.alert, .sound])
    }

    // Handle notification tap
    func userNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void) {
        let userInfo = response.notification.request.content.userInfo

        if let action = userInfo["action"] as? String {
            DispatchQueue.main.async {
                NotificationCenter.default.post(name: .geofenceNotificationTapped, object: action)
            }
        }

        completionHandler()
    }
}

// MARK: - Notification Names
extension Notification.Name {
    static let geofenceNotificationTapped = Notification.Name("geofenceNotificationTapped")
}
