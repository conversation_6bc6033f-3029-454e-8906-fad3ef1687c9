import Foundation
import Network

class APIService: ObservableObject {
    @Published var children: [Child] = []
    @Published var isLoading = false
    @Published var errorMessage = ""
    
    func login(username: String, password: String, token: String, completion: @escaping (Bool, String, String) -> Void) {
        let encodedUsername = username.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
        let encodedPassword = password.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
        let encodedToken = token.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
        
        let urlString = "\(AppConfig.baseURL)/mobile/login?username=\(encodedUsername)&password=\(encodedPassword)&token=\(encodedToken)"
        
        guard let url = URL(string: urlString) else {
            completion(false, "Invalid URL", "")
            return
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("Dart/3.8 (dart:io)", forHTTPHeaderField: "User-Agent")
        request.setValue("gzip, deflate, br", forHTTPHeaderField: "Accept-Encoding")
        request.setValue("0", forHTTPHeaderField: "Content-Length")
        
        URLSession.shared.dataTask(with: request) { data, response, error in
            var jsonResponse = ""
            var sessionCookie = ""
            
            if let data = data {
                jsonResponse = String(data: data, encoding: .utf8) ?? "Could not parse response"
            }
            
            // Extract session cookie from response headers
            if let httpResponse = response as? HTTPURLResponse {
                // Use HTTPCookie to properly parse Set-Cookie headers
                let cookies = HTTPCookie.cookies(withResponseHeaderFields: httpResponse.allHeaderFields as! [String : String], for: url)
                
                for cookie in cookies {
                    if cookie.name == "PLAY_SESSION" {
                        sessionCookie = "PLAY_SESSION=\(cookie.value)"
                        break
                    }
                }
            }
            
            // Always dispatch completion to main thread for UI updates
            DispatchQueue.main.async {
                if let error = error {
                    completion(false, error.localizedDescription, jsonResponse)
                    return
                }
                
                if let httpResponse = response as? HTTPURLResponse {
                    if httpResponse.statusCode == 200 {
                        completion(true, sessionCookie, jsonResponse)
                    } else {
                        completion(false, "HTTP \(httpResponse.statusCode)", jsonResponse)
                    }
                } else {
                    completion(false, "Unknown response", jsonResponse)
                }
            }
        }.resume()
    }
    
    func fetchChildren(sessionCookie: String) {
        guard let url = URL(string: "\(AppConfig.baseURL)/educators/home") else {
            errorMessage = "Invalid URL"
            return
        }
        
        isLoading = true
        errorMessage = ""
        
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.setValue("PLAY_LANG=pt_PT; \(sessionCookie)", forHTTPHeaderField: "Cookie")
        request.setValue("Dart/3.8 (dart:io)", forHTTPHeaderField: "User-Agent")
        request.setValue("gzip, deflate, br", forHTTPHeaderField: "Accept-Encoding")
        
        URLSession.shared.dataTask(with: request) { [weak self] data, response, error in
            DispatchQueue.main.async {
                self?.isLoading = false
                
                if let error = error {
                    self?.errorMessage = "Network error: \(error.localizedDescription)"
                    return
                }
                
                guard let data = data else {
                    self?.errorMessage = "No data received"
                    return
                }
                
                do {
                    let schoolResponse = try JSONDecoder().decode(SchoolResponse.self, from: data)
                    self?.children = schoolResponse.children.map { (id, childData) in
                        Child(
                            id: id,
                            name: childData.name.replacingOccurrences(of: "&eacute;", with: "é")
                                            .replacingOccurrences(of: "&atilde;", with: "ã")
                                            .replacingOccurrences(of: "&oacute;", with: "ó"),
                            photo: childData.photo,
                            school: childData.school
                        )
                    }.sorted { $0.name < $1.name }
                } catch {
                    let friendlyError: String
                    if error is DecodingError {
                        friendlyError = "Unable to load children data. Please try again."
                    } else {
                        friendlyError = "An unexpected error occurred: \(error.localizedDescription)"
                    }
                    self?.errorMessage = friendlyError
                }
            }
        }.resume()
    }
    
    func performCheckInOut(sessionCookie: String, childId: String, notes: String, isCheckIn: Bool, completion: @escaping (Bool, String, String) -> Void) {
        let endpoint = isCheckIn ? "/schoolctrl/savepresencein" : "/schoolctrl/savepresenceout"
        
        guard let url = URL(string: AppConfig.baseURL + endpoint) else {
            completion(false, "Invalid URL", "")
            return
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("PLAY_LANG=pt_PT; \(sessionCookie)", forHTTPHeaderField: "Cookie")
        request.setValue("Dart/3.8 (dart:io)", forHTTPHeaderField: "User-Agent")
        request.setValue("gzip, deflate, br", forHTTPHeaderField: "Accept-Encoding")
        
        // Create multipart form data
        let boundary = "--dio-boundary-2781180372"
        request.setValue("multipart/form-data; boundary=\(boundary)", forHTTPHeaderField: "Content-Type")
        
        let formatter = DateFormatter()
        formatter.dateFormat = "dd-MM-yyyy"
        let currentDate = formatter.string(from: Date())
        
        let calendar = Calendar.current
        let dayOfWeek = calendar.component(.weekday, from: Date())
        
        var body = Data()
        
        // Add form fields
        let formFields: [(String, String)] = [
            ("childId", childId),
            ("colabId", ""),
            ("date", currentDate),
            ("notes", notes),
            ("absent", "false"),
            ("isChecked", "true"),
            ("isEnter", isCheckIn ? "true" : "false"),
            ("numberDay", String(dayOfWeek))
        ]
        
        for (key, value) in formFields {
            body.append("--\(boundary)\r\n".data(using: .utf8)!)
            body.append("content-disposition: form-data; name=\"\(key)\"\r\n\r\n".data(using: .utf8)!)
            body.append("\(value)\r\n".data(using: .utf8)!)
        }
        
        body.append("--\(boundary)--\r\n".data(using: .utf8)!)
        request.httpBody = body
        
        URLSession.shared.dataTask(with: request) { [weak self] data, response, error in
            var jsonResponse = ""
            
            if let data = data {
                if let jsonString = String(data: data, encoding: .utf8) {
                    jsonResponse = jsonString
                } else {
                    jsonResponse = "Response data could not be converted to string"
                }
            }
            
            // Always dispatch completion to main thread for UI updates
            DispatchQueue.main.async {
                if let error = error {
                    completion(false, error.localizedDescription, jsonResponse)
                    return
                }
                
                if let httpResponse = response as? HTTPURLResponse {
                    if httpResponse.statusCode == 200 {
                        completion(true, "Success", jsonResponse)
                    } else {
                        completion(false, "HTTP \(httpResponse.statusCode)", jsonResponse)
                    }
                } else {
                    completion(false, "Unknown response", jsonResponse)
                }
            }
        }.resume()
    }
}
