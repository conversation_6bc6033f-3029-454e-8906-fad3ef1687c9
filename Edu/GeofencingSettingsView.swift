import SwiftUI
import CoreLocation
import MapKit
import UserNotifications

struct GeofencingSettingsView: View {
    @ObservedObject private var geofencingManager = GeofencingManager.shared
    @State private var tempConfig: GeofencingConfig
    @State private var schoolAddress: String = ""
    @State private var radius: Double = 100
    @State private var startHour: Int = 7
    @State private var endHour: Int = 18
    @State private var isEnabled: Bool = false
    @State private var showingLocationAlert = false
    @State private var isSearchingAddress = false
    @State private var searchError: String = ""
    @State private var searchSuccess: String = ""
    @State private var hasUnsavedChanges = false
    @State private var showSaveConfirmation = false
    @State private var mapRegion = MKCoordinateRegion(
        center: CLLocationCoordinate2D(latitude: 38.7223, longitude: -9.1393), // Lisbon default
        span: MKCoordinateSpan(latitudeDelta: 0.01, longitudeDelta: 0.01)
    )
    
    @Environment(\.dismiss) private var dismiss
    
    init() {
        let config = GeofencingManager.shared.config
        _tempConfig = State(initialValue: config)
        _schoolAddress = State(initialValue: config.schoolAddress)
        _radius = State(initialValue: config.radius)
        _startHour = State(initialValue: config.schoolStartHour)
        _endHour = State(initialValue: config.schoolEndHour)
        _isEnabled = State(initialValue: config.isEnabled)
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                DesignSystem.Colors.primaryGradient
                    .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: DesignSystem.Spacing.lg) {
                        // Header
                        VStack(spacing: DesignSystem.Spacing.sm) {
                            Image(systemName: "location.circle.fill")
                                .font(.system(size: 48))
                                .foregroundColor(DesignSystem.Colors.textOnPrimary)
                            
                            Text("Geofencing Settings")
                                .font(DesignSystem.Typography.title1)
                                .foregroundColor(DesignSystem.Colors.textOnPrimary)
                            
                            Text("Get notified when you arrive at or leave school")
                                .font(DesignSystem.Typography.body)
                                .foregroundColor(DesignSystem.Colors.textOnPrimary.opacity(0.8))
                                .multilineTextAlignment(.center)
                        }
                        .padding(.top, DesignSystem.Spacing.lg)
                        
                        // Settings Card
                        VStack(spacing: DesignSystem.Spacing.lg) {
                            // Enable/Disable Toggle
                            VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
                                HStack {
                                    Image(systemName: "bell.fill")
                                        .font(DesignSystem.Typography.footnote)
                                        .foregroundColor(DesignSystem.Colors.textSecondary)
                                    Text("Enable Geofencing")
                                        .font(DesignSystem.Typography.title3)
                                        .foregroundColor(DesignSystem.Colors.textPrimary)
                                    
                                    Spacer()
                                    
                                    Toggle("", isOn: $isEnabled)
                                        .toggleStyle(ProfessionalToggleStyle())
                                        .onChange(of: isEnabled) { _ in
                                            checkForUnsavedChanges()
                                        }
                                }
                                
                                Text("Receive notifications when entering or leaving the school area")
                                    .font(DesignSystem.Typography.caption)
                                    .foregroundColor(DesignSystem.Colors.textSecondary)
                            }
                            
                            if isEnabled {
                                Divider()
                                    .background(DesignSystem.Colors.border)
                                
                                // School Address
                                VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
                                    HStack {
                                        Image(systemName: "building.2.fill")
                                            .font(DesignSystem.Typography.footnote)
                                            .foregroundColor(DesignSystem.Colors.textSecondary)
                                        Text("School Address")
                                            .font(DesignSystem.Typography.title3)
                                            .foregroundColor(DesignSystem.Colors.textPrimary)
                                    }
                                    
                                    HStack {
                                        TextField("Enter school address", text: $schoolAddress)
                                            .textFieldStyle(ProfessionalTextFieldStyle())
                                            .onChange(of: schoolAddress) { _ in
                                                checkForUnsavedChanges()
                                            }
                                        
                                        Button(action: {
                                            searchForAddress()
                                        }) {
                                            HStack(spacing: DesignSystem.Spacing.xs) {
                                                if isSearchingAddress {
                                                    ProgressView()
                                                        .scaleEffect(0.8)
                                                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                                }
                                                Text(isSearchingAddress ? "Searching..." : "Search & Preview")
                                            }
                                        }
                                        .buttonStyle(ProfessionalButtonStyle(color: DesignSystem.Colors.primary, style: .primary))
                                        .disabled(schoolAddress.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || isSearchingAddress)
                                    }
                                    
                                    // Search feedback
                                    if !searchError.isEmpty {
                                        VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                                            HStack(spacing: DesignSystem.Spacing.xs) {
                                                Image(systemName: "exclamationmark.triangle.fill")
                                                    .foregroundColor(DesignSystem.Colors.error)
                                                    .font(.caption)
                                                Text(searchError)
                                                    .font(DesignSystem.Typography.caption)
                                                    .foregroundColor(DesignSystem.Colors.error)
                                                Spacer()
                                            }

                                            Button("Try Again") {
                                                searchForAddress()
                                            }
                                            .buttonStyle(ProfessionalButtonStyle(color: DesignSystem.Colors.secondary, style: .secondary))
                                            .font(.caption)
                                        }
                                        .padding(.top, DesignSystem.Spacing.xs)
                                    }

                                    if !searchSuccess.isEmpty {
                                        HStack(spacing: DesignSystem.Spacing.xs) {
                                            Image(systemName: "checkmark.circle.fill")
                                                .foregroundColor(DesignSystem.Colors.success)
                                                .font(.caption)
                                            Text(searchSuccess)
                                                .font(DesignSystem.Typography.caption)
                                                .foregroundColor(DesignSystem.Colors.success)
                                            Spacer()
                                        }
                                        .padding(.top, DesignSystem.Spacing.xs)
                                    }
                                }
                                
                                // Map Preview
                                if tempConfig.latitude != 0.0 && tempConfig.longitude != 0.0 {
                                    VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
                                        HStack {
                                            Image(systemName: "map.fill")
                                                .font(DesignSystem.Typography.footnote)
                                                .foregroundColor(DesignSystem.Colors.success)
                                            Text("School Location Found")
                                                .font(DesignSystem.Typography.title3)
                                                .foregroundColor(DesignSystem.Colors.textPrimary)
                                            Spacer()

                                            // Status indicator
                                            HStack(spacing: DesignSystem.Spacing.xs) {
                                                Circle()
                                                    .fill(DesignSystem.Colors.success)
                                                    .frame(width: 8, height: 8)
                                                Text("Ready")
                                                    .font(DesignSystem.Typography.caption)
                                                    .foregroundColor(DesignSystem.Colors.success)
                                            }
                                        }
                                        
                                        Map(coordinateRegion: $mapRegion, annotationItems: [SchoolLocation(coordinate: CLLocationCoordinate2D(latitude: tempConfig.latitude, longitude: tempConfig.longitude))]) { location in
                                            MapAnnotation(coordinate: location.coordinate) {
                                                VStack {
                                                    Image(systemName: "building.2.fill")
                                                        .foregroundColor(DesignSystem.Colors.primary)
                                                        .font(.title2)
                                                    
                                                    Circle()
                                                        .stroke(DesignSystem.Colors.primary.opacity(0.3), lineWidth: 2)
                                                        .frame(width: radius * 2, height: radius * 2)
                                                }
                                            }
                                        }
                                        .frame(height: 200)
                                        .cornerRadius(DesignSystem.CornerRadius.medium)
                                        
                                        // Save reminder note
                                        HStack(spacing: 8) {
                                            Image(systemName: "info.circle.fill")
                                                .font(.caption)
                                                .foregroundColor(DesignSystem.Colors.info)
                                            Text("Location found! Tap \"Save\" to activate geofencing.")
                                                .font(DesignSystem.Typography.caption)
                                                .foregroundColor(DesignSystem.Colors.textSecondary)
                                        }
                                        .padding(.top, 4)
                                    }
                                }
                                
                                Divider()
                                    .background(DesignSystem.Colors.border)
                                
                                // Radius Setting
                                VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
                                    HStack {
                                        Image(systemName: "circle.dashed")
                                            .font(DesignSystem.Typography.footnote)
                                            .foregroundColor(DesignSystem.Colors.textSecondary)
                                        Text("Detection Radius")
                                            .font(DesignSystem.Typography.title3)
                                            .foregroundColor(DesignSystem.Colors.textPrimary)
                                        
                                        Spacer()
                                        
                                        Text("\(Int(radius))m")
                                            .font(DesignSystem.Typography.bodyMedium)
                                            .foregroundColor(DesignSystem.Colors.textSecondary)
                                    }
                                    
                                    Slider(value: $radius, in: 50...500, step: 25)
                                        .accentColor(DesignSystem.Colors.primary)
                                        .onChange(of: radius) { _ in
                                            checkForUnsavedChanges()
                                        }
                                    
                                    Text("Distance from school to trigger notifications")
                                        .font(DesignSystem.Typography.caption)
                                        .foregroundColor(DesignSystem.Colors.textSecondary)
                                }
                                
                                Divider()
                                    .background(DesignSystem.Colors.border)
                                
                                // School Hours
                                VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
                                    HStack {
                                        Image(systemName: "clock.fill")
                                            .font(DesignSystem.Typography.footnote)
                                            .foregroundColor(DesignSystem.Colors.textSecondary)
                                        Text("School Hours")
                                            .font(DesignSystem.Typography.title3)
                                            .foregroundColor(DesignSystem.Colors.textPrimary)
                                    }
                                    
                                    HStack {
                                        VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                                            Text("Start Time")
                                                .font(DesignSystem.Typography.caption)
                                                .foregroundColor(DesignSystem.Colors.textSecondary)

                                            Picker("Start Hour", selection: $startHour) {
                                                ForEach(0..<24) { hour in
                                                    Text(formatHour(hour)).tag(hour)
                                                }
                                            }
                                            .pickerStyle(MenuPickerStyle())
                                            .frame(maxWidth: .infinity)
                                            .onChange(of: startHour) { _ in
                                                checkForUnsavedChanges()
                                            }
                                        }

                                        VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                                            Text("End Time")
                                                .font(DesignSystem.Typography.caption)
                                                .foregroundColor(DesignSystem.Colors.textSecondary)

                                            Picker("End Hour", selection: $endHour) {
                                                ForEach(0..<24) { hour in
                                                    Text(formatHour(hour)).tag(hour)
                                                }
                                            }
                                            .pickerStyle(MenuPickerStyle())
                                            .frame(maxWidth: .infinity)
                                            .onChange(of: endHour) { _ in
                                                checkForUnsavedChanges()
                                            }
                                        }
                                    }

                                    Text("Monday to Friday, \(formatHour(startHour)) - \(formatHour(endHour))")
                                        .font(DesignSystem.Typography.caption)
                                        .foregroundColor(DesignSystem.Colors.textSecondary)
                                }
                                
                                // Location Permission Status
                                if geofencingManager.locationPermissionStatus != .authorizedAlways {
                                    VStack(spacing: DesignSystem.Spacing.sm) {
                                        HStack {
                                            Image(systemName: "exclamationmark.triangle.fill")
                                                .foregroundColor(DesignSystem.Colors.warning)
                                            Text("Location Permission Required")
                                                .font(DesignSystem.Typography.bodyMedium)
                                                .foregroundColor(DesignSystem.Colors.textPrimary)
                                        }
                                        
                                        Text("Always allow location access is required for geofencing to work in the background.")
                                            .font(DesignSystem.Typography.caption)
                                            .foregroundColor(DesignSystem.Colors.textSecondary)
                                            .multilineTextAlignment(.center)
                                        
                                        Button("Grant Permission") {
                                            geofencingManager.requestLocationPermission()
                                        }
                                        .buttonStyle(ProfessionalButtonStyle(color: DesignSystem.Colors.warning, style: .primary))
                                    }
                                    .padding(DesignSystem.Spacing.md)
                                    .background(
                                        RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
                                            .fill(DesignSystem.Colors.warning.opacity(0.1))
                                    )
                                }
                            }
                        }
                        .padding(DesignSystem.Spacing.lg)
                        .background(
                            RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.extraLarge)
                                .fill(DesignSystem.Colors.surface)
                                .shadow(
                                    color: DesignSystem.Shadow.large.color,
                                    radius: DesignSystem.Shadow.large.radius,
                                    x: DesignSystem.Shadow.large.x,
                                    y: DesignSystem.Shadow.large.y
                                )
                        )
                        .padding(.horizontal, DesignSystem.Spacing.lg)
                    }
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .overlay(
                // Save confirmation overlay
                Group {
                    if showSaveConfirmation {
                        VStack(spacing: DesignSystem.Spacing.md) {
                            HStack(spacing: DesignSystem.Spacing.sm) {
                                Image(systemName: "checkmark.circle.fill")
                                    .foregroundColor(DesignSystem.Colors.success)
                                    .font(.title2)

                                VStack(alignment: .leading, spacing: 2) {
                                    Text("Settings Saved!")
                                        .font(DesignSystem.Typography.bodyMedium)
                                        .foregroundColor(DesignSystem.Colors.textPrimary)

                                    Text("Geofencing is now active")
                                        .font(DesignSystem.Typography.caption)
                                        .foregroundColor(DesignSystem.Colors.textSecondary)
                                }

                                Spacer()
                            }
                            .padding(DesignSystem.Spacing.md)
                            .background(
                                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
                                    .fill(DesignSystem.Colors.success.opacity(0.1))
                                    .overlay(
                                        RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
                                            .stroke(DesignSystem.Colors.success.opacity(0.3), lineWidth: 1)
                                    )
                            )
                        }
                        .padding(.horizontal, DesignSystem.Spacing.lg)
                        .transition(.move(edge: .top).combined(with: .opacity))
                        .animation(.spring(response: 0.6, dampingFraction: 0.8), value: showSaveConfirmation)
                    }
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .top)
                .padding(.top, 100)
            )
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(DesignSystem.Colors.textOnPrimary)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        saveSettings()
                    }) {
                        HStack(spacing: 4) {
                            if hasUnsavedChanges {
                                Circle()
                                    .fill(DesignSystem.Colors.warning)
                                    .frame(width: 6, height: 6)
                            }
                            Text("Save")
                        }
                    }
                    .foregroundColor(hasUnsavedChanges ? DesignSystem.Colors.warning : DesignSystem.Colors.textOnPrimary)
                    .fontWeight(.semibold)
                }
            }
        }
    }

    private func formatHour(_ hour: Int) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "h:mm a"

        let calendar = Calendar.current
        let date = calendar.date(bySettingHour: hour, minute: 0, second: 0, of: Date()) ?? Date()
        return formatter.string(from: date)
    }

    private func searchForAddress() {
        guard !schoolAddress.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            searchError = "Please enter a school address"
            return
        }

        isSearchingAddress = true
        searchError = ""
        searchSuccess = ""

        let geocoder = CLGeocoder()
        let trimmedAddress = schoolAddress.trimmingCharacters(in: .whitespacesAndNewlines)

        geocoder.geocodeAddressString(trimmedAddress) { placemarks, error in
            DispatchQueue.main.async {
                self.isSearchingAddress = false

                if let error = error {
                    let nsError = error as NSError
                    switch nsError.code {
                    case CLError.network.rawValue:
                        self.searchError = "No internet connection. Please check your network and try again."
                    case CLError.geocodeFoundNoResult.rawValue:
                        self.searchError = "Address not found. Please check the spelling and try again."
                    case CLError.geocodeFoundPartialResult.rawValue:
                        self.searchError = "Partial address found. Please be more specific."
                    case CLError.geocodeCanceled.rawValue:
                        self.searchError = "Search was cancelled. Please try again."
                    default:
                        self.searchError = "Unable to find address. Please try a different search term."
                    }
                    return
                }

                guard let placemark = placemarks?.first,
                      let location = placemark.location else {
                    self.searchError = "Address found but location unavailable. Please try again."
                    return
                }

                // Validate that we have a reasonable location
                let coordinate = location.coordinate
                if coordinate.latitude == 0.0 && coordinate.longitude == 0.0 {
                    self.searchError = "Invalid location found. Please try a more specific address."
                    return
                }

                self.tempConfig = GeofencingConfig(
                    isEnabled: self.isEnabled,
                    schoolAddress: trimmedAddress,
                    latitude: coordinate.latitude,
                    longitude: coordinate.longitude,
                    radius: self.radius,
                    schoolStartHour: self.startHour,
                    schoolEndHour: self.endHour,
                    schoolDays: [2, 3, 4, 5, 6] // Monday to Friday
                )

                // Update map region with appropriate zoom level
                self.mapRegion = MKCoordinateRegion(
                    center: coordinate,
                    span: MKCoordinateSpan(latitudeDelta: 0.005, longitudeDelta: 0.005)
                )

                // Clear any previous errors and show success
                self.searchError = ""
                self.searchSuccess = "✓ Location found and ready to save!"
                self.hasUnsavedChanges = true
            }
        }
    }

    private func checkForUnsavedChanges() {
        let currentConfig = geofencingManager.config

        hasUnsavedChanges = (
            isEnabled != currentConfig.isEnabled ||
            schoolAddress != currentConfig.schoolAddress ||
            radius != currentConfig.radius ||
            startHour != currentConfig.schoolStartHour ||
            endHour != currentConfig.schoolEndHour ||
            tempConfig.latitude != currentConfig.latitude ||
            tempConfig.longitude != currentConfig.longitude
        )
    }

    private func saveSettings() {
        let newConfig = GeofencingConfig(
            isEnabled: isEnabled,
            schoolAddress: schoolAddress,
            latitude: tempConfig.latitude,
            longitude: tempConfig.longitude,
            radius: radius,
            schoolStartHour: startHour,
            schoolEndHour: endHour,
            schoolDays: [2, 3, 4, 5, 6] // Monday to Friday
        )

        geofencingManager.updateConfig(newConfig)

        // Show save confirmation
        hasUnsavedChanges = false
        showSaveConfirmation = true

        // Dismiss after a short delay to show confirmation
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            dismiss()
        }
    }
}

// Helper struct for map annotations
struct SchoolLocation: Identifiable {
    let id = UUID()
    let coordinate: CLLocationCoordinate2D
}

#Preview {
    GeofencingSettingsView()
}
