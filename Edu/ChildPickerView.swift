import SwiftUI

struct ChildPickerView: View {
    let children: [Child]
    @Binding var selectedChild: Child?
    @Binding var isPresented: Bool
    
    var body: some View {
        NavigationView {
            ZStack {
                // Enhanced Background gradient
                DesignSystem.Colors.primaryGradient
                    .ignoresSafeArea()

                VStack {
                    // Enhanced Title
                    Text("Select Child")
                        .font(DesignSystem.Typography.title1)
                        .foregroundColor(DesignSystem.Colors.textOnPrimary)
                        .padding(.top, DesignSystem.Spacing.lg)
                    
                    // Children List
                    ScrollView {
                        VStack(spacing: 12) {
                            ForEach(children, id: \.id) { child in
                                Button(action: {
                                    selectedChild = child
                                    isPresented = false
                                }) {
                                    HStack {
                                        VStack(alignment: .leading, spacing: 4) {
                                            Text(child.name)
                                                .font(.system(size: 18, weight: .semibold))
                                                .foregroundColor(.black)
                                            
                                            Text("ID: \(child.id)")
                                                .font(.system(size: 14))
                                                .foregroundColor(.gray)
                                        }
                                        
                                        Spacer()
                                        
                                        if selectedChild?.id == child.id {
                                            Image(systemName: "checkmark.circle.fill")
                                                .font(.system(size: 20))
                                                .foregroundColor(.blue)
                                        } else {
                                            Image(systemName: "circle")
                                                .font(.system(size: 20))
                                                .foregroundColor(.gray)
                                        }
                                    }
                                    .padding(.horizontal, 20)
                                    .padding(.vertical, 16)
                                    .background(
                                        RoundedRectangle(cornerRadius: 12)
                                            .fill(Color.white.opacity(0.9))
                                    )
                                    .accessibilityLabel("Select \(child.name), ID: \(child.id)")
                                    .accessibilityHint(selectedChild?.id == child.id ? "Currently selected" : "Tap to select this child")
                                }
                                .buttonStyle(PlainButtonStyle())
                            }
                        }
                        .padding(.horizontal, 20)
                    }
                    
                    // Cancel Button
                    Button("Cancel") {
                        isPresented = false
                    }
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                    .padding(.horizontal, 40)
                    .padding(.vertical, 16)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(.ultraThinMaterial)
                    )
                    .padding(.bottom, 40)
                }
            }
        }
    }
}
